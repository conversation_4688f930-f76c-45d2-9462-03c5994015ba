import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/Collapsible';
import { 
  ChevronDown, 
  ChevronRight, 
  AlertTriangle, 
  Clock,
  TrendingUp,
  CheckSquare,
  ExternalLink,
  Lightbulb
} from 'lucide-react';
import { 
  HipaaPrivacyRecommendation,
  HipaaPrivacyPriority,
  HipaaPrivacyEffort,
  HipaaPrivacyImpact,
  HipaaPrivacyCheckCategory
} from '@/types/hipaa-privacy';

interface RecommendationsListProps {
  recommendations: HipaaPrivacyRecommendation[];
}

export const RecommendationsList: React.FC<RecommendationsListProps> = ({
  recommendations,
}) => {
  const [expandedRecommendations, setExpandedRecommendations] = useState<Set<string>>(new Set());

  const toggleExpanded = (recommendationId: string) => {
    const newExpanded = new Set(expandedRecommendations);
    if (newExpanded.has(recommendationId)) {
      newExpanded.delete(recommendationId);
    } else {
      newExpanded.add(recommendationId);
    }
    setExpandedRecommendations(newExpanded);
  };

  // Helper function to convert numeric priority to string label
  const getPriorityLabel = (priority: number | string): HipaaPrivacyPriority => {
    if (typeof priority === 'number') {
      switch (priority) {
        case 1:
          return 'critical';
        case 2:
          return 'high';
        case 3:
          return 'medium';
        case 4:
        case 5:
        default:
          return 'low';
      }
    }
    return priority as HipaaPrivacyPriority;
  };

  const getPriorityColor = (priority: HipaaPrivacyPriority): string => {
    switch (priority) {
      case 'critical':
        return 'bg-red-500 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getEffortColor = (effort: HipaaPrivacyEffort): string => {
    switch (effort) {
      case 'minimal':
        return 'bg-green-100 text-green-800';
      case 'moderate':
        return 'bg-yellow-100 text-yellow-800';
      case 'significant':
        return 'bg-orange-100 text-orange-800';
      case 'extensive':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getImpactColor = (impact: HipaaPrivacyImpact): string => {
    switch (impact) {
      case 'high':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: HipaaPrivacyCheckCategory): string => {
    switch (category) {
      case HipaaPrivacyCheckCategory.PRESENCE:
        return 'bg-purple-100 text-purple-800';
      case HipaaPrivacyCheckCategory.ACCESSIBILITY:
        return 'bg-blue-100 text-blue-800';
      case HipaaPrivacyCheckCategory.CONTENT_STRUCTURE:
        return 'bg-green-100 text-green-800';
      case HipaaPrivacyCheckCategory.HIPAA_SPECIFIC:
        return 'bg-red-100 text-red-800';
      case HipaaPrivacyCheckCategory.QUALITY:
        return 'bg-yellow-100 text-yellow-800';
      case HipaaPrivacyCheckCategory.LEGAL_COMPLIANCE:
        return 'bg-orange-100 text-orange-800';
      case HipaaPrivacyCheckCategory.CONTACT_INFO:
        return 'bg-teal-100 text-teal-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityIcon = (priority: HipaaPrivacyPriority) => {
    switch (priority) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-4 w-4" />;
      case 'medium':
        return <TrendingUp className="h-4 w-4" />;
      case 'low':
        return <Lightbulb className="h-4 w-4" />;
      default:
        return <Lightbulb className="h-4 w-4" />;
    }
  };

  // Sort recommendations by priority
  const sortedRecommendations = [...recommendations].sort((a, b) => {
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    const aPriority = getPriorityLabel(a.priority);
    const bPriority = getPriorityLabel(b.priority);
    return priorityOrder[aPriority] - priorityOrder[bPriority];
  });

  if (recommendations.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <CheckSquare className="h-12 w-12 text-green-400 mx-auto mb-4" />
        <p className="text-lg font-medium">No recommendations needed</p>
        <p className="text-sm">Your privacy policy appears to be fully compliant!</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {sortedRecommendations.map((recommendation) => {
        const isExpanded = expandedRecommendations.has(recommendation.id);
        
        return (
          <Card key={recommendation.id} className="overflow-hidden">
            <Collapsible open={isExpanded} onOpenChange={() => toggleExpanded(recommendation.id)}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getPriorityIcon(getPriorityLabel(recommendation.priority))}
                      <div className="flex-1">
                        <CardTitle className="text-lg">{recommendation.title}</CardTitle>
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                          {recommendation.description}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(getPriorityLabel(recommendation.priority))}>
                        {getPriorityLabel(recommendation.priority).toUpperCase()}
                      </Badge>
                      <Badge variant="outline" className={getEffortColor(recommendation.effort)}>
                        {recommendation.effort.toUpperCase()} EFFORT
                      </Badge>
                      <Badge variant="outline" className={getImpactColor(recommendation.impact)}>
                        {recommendation.impact.toUpperCase()} IMPACT
                      </Badge>
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4 text-gray-500" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-500" />
                      )}
                    </div>
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              
              <CollapsibleContent>
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {/* Category and Timeline */}
                    <div className="flex items-center gap-4">
                      <Badge variant="outline" className={getCategoryColor(recommendation.category)}>
                        {recommendation.category.replace('_', ' ').toUpperCase()}
                      </Badge>
                      {recommendation.timeline && (
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                          <Clock className="h-4 w-4" />
                          <span>Estimated time: {recommendation.timeline}</span>
                        </div>
                      )}
                    </div>

                    {/* Full Description */}
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">Description</h4>
                      <p className="text-sm text-gray-700">{recommendation.description}</p>
                    </div>

                    {/* Action Items */}
                    {recommendation.actionItems && recommendation.actionItems.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-gray-900 mb-3">
                          Action Items ({recommendation.actionItems.length})
                        </h4>
                        <ul className="space-y-2">
                          {recommendation.actionItems.map((item, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <CheckSquare className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                              <span className="text-sm text-gray-700">{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Resources */}
                    {recommendation.resources && recommendation.resources.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-gray-900 mb-3">
                          Helpful Resources
                        </h4>
                        <ul className="space-y-2">
                          {recommendation.resources.map((resource, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <ExternalLink className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                              <span className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer">
                                {resource}
                              </span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Implementation Metrics */}
                    <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded">
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-900">
                          {getPriorityLabel(recommendation.priority).toUpperCase()}
                        </div>
                        <div className="text-xs text-gray-500">Priority</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-900">
                          {recommendation.effort.toUpperCase()}
                        </div>
                        <div className="text-xs text-gray-500">Effort Required</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-900">
                          {recommendation.impact.toUpperCase()}
                        </div>
                        <div className="text-xs text-gray-500">Expected Impact</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        );
      })}
    </div>
  );
};
