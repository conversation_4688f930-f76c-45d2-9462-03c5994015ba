import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { 
  Shield, 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  TrendingUp,
  Clock,
  Users,
  Scale
} from 'lucide-react';
import { 
  HipaaPrivacyScanSummary,
  HipaaPrivacyScanResult,
  ComplianceSummaryProps,
  HipaaPrivacyCheckCategory
} from '@/types/hipaa-privacy';

export const ComplianceSummary: React.FC<ComplianceSummaryProps> = ({
  summary,
  scanResult,
}) => {
  const getRiskLevelColor = (riskLevel: string): string => {
    switch (riskLevel) {
      case 'critical':
        return 'text-red-600';
      case 'high':
        return 'text-orange-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  const getRiskLevelBg = (riskLevel: string): string => {
    switch (riskLevel) {
      case 'critical':
        return 'bg-red-100 border-red-200';
      case 'high':
        return 'bg-orange-100 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 border-yellow-200';
      case 'low':
        return 'bg-green-100 border-green-200';
      default:
        return 'bg-gray-100 border-gray-200';
    }
  };

  // Calculate category breakdown
  const categoryBreakdown = scanResult.checks.reduce((acc, check) => {
    if (!acc[check.category]) {
      acc[check.category] = { total: 0, passed: 0, failed: 0 };
    }
    acc[check.category].total++;
    if (check.passed) {
      acc[check.category].passed++;
    } else {
      acc[check.category].failed++;
    }
    return acc;
  }, {} as Record<HipaaPrivacyCheckCategory, { total: number; passed: number; failed: number }>);

  const getCategoryDisplayName = (category: HipaaPrivacyCheckCategory): string => {
    switch (category) {
      case HipaaPrivacyCheckCategory.PRESENCE:
        return 'Policy Presence';
      case HipaaPrivacyCheckCategory.ACCESSIBILITY:
        return 'Accessibility';
      case HipaaPrivacyCheckCategory.CONTENT_STRUCTURE:
        return 'Content Structure';
      case HipaaPrivacyCheckCategory.HIPAA_SPECIFIC:
        return 'HIPAA Specific';
      case HipaaPrivacyCheckCategory.QUALITY:
        return 'Content Quality';
      case HipaaPrivacyCheckCategory.LEGAL_COMPLIANCE:
        return 'Legal Compliance';
      case HipaaPrivacyCheckCategory.CONTACT_INFO:
        return 'Contact Information';
      default:
        return category.replace('_', ' ');
    }
  };

  const getCategoryIcon = (category: HipaaPrivacyCheckCategory) => {
    switch (category) {
      case HipaaPrivacyCheckCategory.PRESENCE:
        return <FileText className="h-4 w-4" />;
      case HipaaPrivacyCheckCategory.ACCESSIBILITY:
        return <Users className="h-4 w-4" />;
      case HipaaPrivacyCheckCategory.CONTENT_STRUCTURE:
        return <TrendingUp className="h-4 w-4" />;
      case HipaaPrivacyCheckCategory.HIPAA_SPECIFIC:
        return <Shield className="h-4 w-4" />;
      case HipaaPrivacyCheckCategory.QUALITY:
        return <CheckCircle className="h-4 w-4" />;
      case HipaaPrivacyCheckCategory.LEGAL_COMPLIANCE:
        return <Scale className="h-4 w-4" />;
      case HipaaPrivacyCheckCategory.CONTACT_INFO:
        return <Users className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Overall Risk Assessment */}
      <Card className={`border-2 ${getRiskLevelBg(summary.riskLevel)}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Overall Risk Assessment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className={`text-3xl font-bold ${getRiskLevelColor(summary.riskLevel)}`}>
                {summary.riskLevel.toUpperCase()} RISK
              </div>
              <div className="text-sm text-gray-600">
                Based on {summary.totalChecks} compliance checks
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">
                {summary.compliancePercentage}%
              </div>
              <div className="text-sm text-gray-600">Compliant</div>
            </div>
          </div>
          <Progress value={summary.compliancePercentage} className="h-3" />
        </CardContent>
      </Card>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Checks</p>
                <p className="text-2xl font-bold">{summary.totalChecks}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Passed</p>
                <p className="text-2xl font-bold text-green-600">{summary.passedChecks}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Failed</p>
                <p className="text-2xl font-bold text-red-600">{summary.failedChecks}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical Issues</p>
                <p className="text-2xl font-bold text-red-600">{summary.criticalIssues}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Issue Severity Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Issue Severity Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-red-50 rounded">
              <div className="text-2xl font-bold text-red-600">{summary.criticalIssues}</div>
              <div className="text-sm text-red-700">Critical</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded">
              <div className="text-2xl font-bold text-orange-600">{summary.highIssues}</div>
              <div className="text-sm text-orange-700">High</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded">
              <div className="text-2xl font-bold text-yellow-600">{summary.mediumIssues}</div>
              <div className="text-sm text-yellow-700">Medium</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded">
              <div className="text-2xl font-bold text-blue-600">{summary.lowIssues}</div>
              <div className="text-sm text-blue-700">Low</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Category Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Category Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(categoryBreakdown).map(([category, stats]) => {
              const percentage = stats.total > 0 ? Math.round((stats.passed / stats.total) * 100) : 0;
              const categoryEnum = category as HipaaPrivacyCheckCategory;
              
              return (
                <div key={category} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(categoryEnum)}
                      <span className="font-medium">{getCategoryDisplayName(categoryEnum)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">
                        {stats.passed}/{stats.total}
                      </span>
                      <Badge variant={percentage >= 80 ? 'default' : 'destructive'}>
                        {percentage}%
                      </Badge>
                    </div>
                  </div>
                  <Progress value={percentage} className="h-2" />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Scan Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Scan Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-600">Scan Timestamp:</span>
              <span className="ml-2">{new Date(scanResult.timestamp).toLocaleString()}</span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Checks Performed:</span>
              <span className="ml-2">{scanResult.metadata.checksPerformed}</span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Analysis Levels:</span>
              <span className="ml-2">{scanResult.metadata.analysisLevelsUsed.join(', ')}</span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Cache Hits:</span>
              <span className="ml-2">{scanResult.metadata.cacheHits}</span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Version:</span>
              <span className="ml-2">{scanResult.metadata.version}</span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Processing Time:</span>
              <span className="ml-2">{Math.round(scanResult.metadata.processingTime / 1000)}s</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
