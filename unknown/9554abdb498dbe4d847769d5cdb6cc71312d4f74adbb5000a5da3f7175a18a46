// backend/src/compliance/hipaa/privacy/utils/url-resolver.ts

import axios, { AxiosInstance } from 'axios';
import * as https from 'https';
import { JSDOM } from 'jsdom';
import { PrivacyPolicyLink } from '../types';
import { PERFORMANCE_CONFIG, ERROR_CODES } from '../constants';

/**
 * Validated privacy policy link with accessibility information
 */
export interface ValidatedLink extends PrivacyPolicyLink {
  validated: boolean;
  contentLength?: number;
  error?: string;
  responseTime?: number;
  statusCode?: number;
}

/**
 * URL resolution utilities for finding and validating privacy policy pages
 * Handles the discovery phase of the 3-level analysis workflow
 */
export class URLResolver {
  private static httpClient: AxiosInstance;

  /**
   * Initialize the secure HTTP client
   */
  private static initializeHttpClient(): void {
    if (this.httpClient !== undefined) return;

    this.httpClient = axios.create({
      timeout: PERFORMANCE_CONFIG.TIMEOUTS.PAGE_FETCH,
      maxRedirects: 5,
      maxContentLength: 10 * 1024 * 1024, // 10MB limit
      httpsAgent: new https.Agent({
        rejectUnauthorized: true,
        secureProtocol: 'TLSv1_2_method',
      }),
      headers: {
        // Use realistic browser User-Agent to avoid bot detection
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        Connection: 'keep-alive',
      },
    });

    // Request interceptor for security validation
    this.httpClient.interceptors.request.use((config) => {
      if (config.url && !this.isAllowedUrl(config.url)) {
        throw new Error('URL not allowed for security reasons');
      }
      return config;
    });
  }

  /**
   * Finds privacy policy links on a webpage
   * Returns all potential privacy policy links found
   */
  static findPrivacyPolicyLinks(html: string, baseUrl?: string): PrivacyPolicyLink[] {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;
      const links = Array.from(document.querySelectorAll('a[href]'));
      const privacyLinks: PrivacyPolicyLink[] = [];

      // Privacy policy link patterns (enhanced to catch more variations)
      const privacyPatterns = [
        /privacy\s*policy/i,
        /privacy\s*notice/i,
        /hipaa\s*notice/i,
        /notice\s*of\s*privacy\s*practices/i,
        /privacy\s*statement/i,
        /data\s*privacy/i,
        /\bprivacy\b/i, // Just "Privacy" link
        /data\s*protection/i,
        /cookie\s*policy/i,
        /terms\s*.*\s*privacy/i,
        // Enhanced patterns for healthcare/insurance sites
        /legal\s*notices?/i,
        /legal\s*&?\s*policy\s*info/i,
        /plan\s*disclosures?/i,
        /privacy\s*center/i,
        /privacy\s*hub/i,
        /program\s*provisions/i,
        /member\s*privacy/i,
        /patient\s*privacy/i,
        /health\s*information\s*privacy/i,
        /protected\s*health\s*information/i,
        /phi\s*policy/i,
        /compliance\s*notices?/i,
        /regulatory\s*notices?/i,
        /legal\s*information/i,
        /terms\s*of\s*use/i, // Often contains privacy info
        /website\s*security/i,
        /fraud\s*prevention/i, // Healthcare sites often link privacy from fraud prevention
        /non.?discrimination\s*notice/i,
      ];

      for (const link of links) {
        const href = link.getAttribute('href');
        const text = link.textContent?.trim() || '';

        if (!href || !text) continue;

        // Check if link text matches privacy patterns
        const isPrivacyLink = privacyPatterns.some((pattern) => pattern.test(text));

        // Also check href for privacy indicators
        const hasPrivacyInUrl = privacyPatterns.some((pattern) => pattern.test(href));

        // Additional URL path patterns for healthcare/insurance sites
        const urlPathPatterns = [
          /\/privacy/i,
          /\/legal/i,
          /\/notices?/i,
          /\/compliance/i,
          /\/hipaa/i,
          /\/phi/i,
          /\/terms/i,
          /\/disclosures?/i,
          /\/security/i,
          /\/fraud/i,
          /\/member.*privacy/i,
          /\/patient.*privacy/i,
        ];

        const hasPrivacyInPath = urlPathPatterns.some((pattern) => pattern.test(href));

        if (isPrivacyLink || hasPrivacyInUrl || hasPrivacyInPath) {
          const absoluteUrl = this.resolveUrl(href, baseUrl);

          if (absoluteUrl) {
            privacyLinks.push({
              url: absoluteUrl,
              text: text,
              type: this.classifyLinkType(text, href),
              accessible: true, // Will be validated separately
              format: this.detectLinkFormat(href),
            });
          }
        }
      }

      // Remove duplicates based on URL
      const uniqueLinks = privacyLinks.filter(
        (link, index, array) => array.findIndex((l) => l.url === link.url) === index,
      );

      return uniqueLinks;
    } catch (error) {
      console.error('Error finding privacy policy links:', error);
      return [];
    }
  }

  /**
   * Validates that privacy policy pages are accessible and contain real content
   */
  static async validatePolicyPages(links: PrivacyPolicyLink[]): Promise<ValidatedLink[]> {
    this.initializeHttpClient();
    const validatedLinks: ValidatedLink[] = [];

    for (const link of links) {
      const startTime = Date.now();

      try {
        const response = await this.httpClient.get(link.url);
        const responseTime = Date.now() - startTime;

        // Validate that the page contains actual policy content
        const isValidPolicy = this.isValidPrivacyPolicy(response.data);

        validatedLinks.push({
          ...link,
          validated: isValidPolicy,
          contentLength: response.data.length,
          responseTime,
          statusCode: response.status,
        });
      } catch (error) {
        const responseTime = Date.now() - startTime;

        validatedLinks.push({
          ...link,
          validated: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          responseTime,
          statusCode: axios.isAxiosError(error) ? error.response?.status : undefined,
        });
      }
    }

    return validatedLinks;
  }

  /**
   * Downloads and returns the content of privacy policy pages
   */
  static async resolvePrivacyPages(links: ValidatedLink[]): Promise<
    Array<{
      link: ValidatedLink;
      content: string;
      error?: string;
    }>
  > {
    this.initializeHttpClient();
    const results: Array<{ link: ValidatedLink; content: string; error?: string }> = [];

    for (const link of links.filter((l) => l.validated)) {
      try {
        const response = await this.httpClient.get(link.url);

        results.push({
          link,
          content: response.data,
        });
      } catch (error) {
        results.push({
          link,
          content: '',
          error: error instanceof Error ? error.message : 'Failed to fetch content',
        });
      }
    }

    return results;
  }

  /**
   * Validates that a page contains actual privacy policy content
   */
  private static isValidPrivacyPolicy(content: string): boolean {
    if (!content || content.length < 500) {
      return false; // Too short to be a real policy
    }

    // Check for common privacy policy indicators
    const privacyIndicators = [
      /privacy/i,
      /information/i,
      /data/i,
      /collect/i,
      /use/i,
      /share/i,
      /protect/i,
    ];

    const indicatorCount = privacyIndicators.filter((indicator) => indicator.test(content)).length;

    // Should have at least 4 out of 7 indicators
    if (indicatorCount < 4) {
      return false;
    }

    // Check for "coming soon" or placeholder content
    const placeholderPatterns = [
      /coming\s*soon/i,
      /under\s*construction/i,
      /page\s*not\s*found/i,
      /404/i,
      /lorem\s*ipsum/i,
    ];

    const hasPlaceholder = placeholderPatterns.some((pattern) => pattern.test(content));

    return !hasPlaceholder;
  }

  /**
   * Resolves relative URLs to absolute URLs
   */
  static resolveUrl(href: string, baseUrl?: string): string | null {
    try {
      // If href is already absolute, return it
      if (href.startsWith('http://') || href.startsWith('https://')) {
        return href;
      }

      // If no base URL provided, can't resolve relative URLs
      if (!baseUrl) {
        return null;
      }

      // Resolve relative URL
      const base = new URL(baseUrl);
      const resolved = new URL(href, base);

      return resolved.toString();
    } catch (error) {
      console.warn('Failed to resolve URL:', href, error);
      return null;
    }
  }

  /**
   * Classifies the type of privacy link
   */
  private static classifyLinkType(
    text: string,
    href: string,
  ): 'privacy_policy' | 'privacy_notice' | 'hipaa_notice' | 'legal_notice' | 'compliance_notice' {
    const lowerText = text.toLowerCase();
    const lowerHref = href.toLowerCase();

    // HIPAA-specific notices
    if (
      lowerText.includes('hipaa') ||
      lowerHref.includes('hipaa') ||
      lowerText.includes('notice of privacy practices') ||
      lowerText.includes('protected health information') ||
      lowerText.includes('phi policy')
    ) {
      return 'hipaa_notice';
    }

    // Legal notices and disclosures
    if (
      lowerText.includes('legal notice') ||
      lowerText.includes('plan disclosure') ||
      lowerText.includes('program provision') ||
      lowerText.includes('regulatory notice') ||
      lowerHref.includes('legal') ||
      lowerHref.includes('disclosure')
    ) {
      return 'legal_notice';
    }

    // Compliance notices
    if (
      lowerText.includes('compliance') ||
      lowerText.includes('fraud prevention') ||
      lowerText.includes('non-discrimination') ||
      lowerHref.includes('compliance')
    ) {
      return 'compliance_notice';
    }

    // Privacy notices
    if (
      lowerText.includes('notice') ||
      lowerHref.includes('notice') ||
      lowerText.includes('privacy center') ||
      lowerText.includes('privacy hub')
    ) {
      return 'privacy_notice';
    }

    return 'privacy_policy';
  }

  /**
   * Detects the format of a link
   */
  private static detectLinkFormat(href: string): 'html' | 'pdf' | 'doc' | 'txt' {
    const lowerHref = href.toLowerCase();

    if (lowerHref.endsWith('.pdf') || lowerHref.includes('.pdf?')) {
      return 'pdf';
    }

    if (
      lowerHref.endsWith('.doc') ||
      lowerHref.endsWith('.docx') ||
      lowerHref.includes('.doc?') ||
      lowerHref.includes('.docx?')
    ) {
      return 'doc';
    }

    if (lowerHref.endsWith('.txt') || lowerHref.includes('.txt?')) {
      return 'txt';
    }

    return 'html';
  }

  /**
   * Validates if a URL is allowed for security reasons
   */
  private static isAllowedUrl(url: string): boolean {
    try {
      const parsed = new URL(url);

      // Block private IP ranges
      const hostname = parsed.hostname;
      if (this.isPrivateIP(hostname)) {
        return false;
      }

      // Block localhost and loopback
      if (['localhost', '127.0.0.1', '0.0.0.0', '::1'].includes(hostname)) {
        return false;
      }

      // Only allow HTTP and HTTPS
      if (!['http:', 'https:'].includes(parsed.protocol)) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Checks if hostname is a private IP address
   */
  private static isPrivateIP(hostname: string): boolean {
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^169\.254\./,
      /^fc00:/,
      /^fe80:/,
    ];

    return privateRanges.some((range) => range.test(hostname));
  }

  /**
   * Fetches page content with error handling and bot protection bypass
   */
  static async fetchPageContent(
    url: string,
    options?: { timeout?: number; userAgent?: string },
  ): Promise<string> {
    this.initializeHttpClient();

    // Different User-Agent strings to try if bot protection is detected
    const userAgents = [
      // Default realistic browser
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      // Alternative browsers
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
      // Mobile browsers
      'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
    ];

    const startUserAgent = options?.userAgent || userAgents[0];

    // Try with the specified or default User-Agent first
    try {
      const response = await this.attemptFetch(url, startUserAgent, options?.timeout);

      // Check if we got a security block
      if (this.isSecurityBlock(response.data)) {
        console.warn(`🛡️ [Bot Protection] Security block detected for ${url}, trying alternative approaches...`);

        // Try different User-Agents
        for (const userAgent of userAgents) {
          if (userAgent === startUserAgent) continue; // Skip the one we already tried

          try {
            console.log(`🔄 [Bot Protection] Retrying with different User-Agent: ${userAgent.substring(0, 50)}...`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between attempts

            const retryResponse = await this.attemptFetch(url, userAgent, options?.timeout);

            if (!this.isSecurityBlock(retryResponse.data)) {
              console.log(`✅ [Bot Protection] Successfully bypassed with alternative User-Agent`);
              return retryResponse.data;
            }
          } catch (retryError) {
            console.warn(`⚠️ [Bot Protection] Retry failed with User-Agent ${userAgent.substring(0, 30)}:`, retryError);
            continue;
          }
        }

        console.warn(`🚫 [Bot Protection] All User-Agent attempts failed, returning blocked content`);
      }

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new Error(`${ERROR_CODES.TIMEOUT}: Request timed out`);
        }
        if (error.response?.status === 404) {
          throw new Error(`${ERROR_CODES.FETCH_FAILED}: Page not found`);
        }
        if (error.response?.status && error.response.status >= 500) {
          throw new Error(`${ERROR_CODES.FETCH_FAILED}: Server error`);
        }
      }

      throw new Error(
        `${ERROR_CODES.FETCH_FAILED}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Attempts to fetch content with a specific User-Agent
   */
  private static async attemptFetch(url: string, userAgent: string, timeout?: number): Promise<{ data: string }> {
    const config: { timeout?: number; headers: Record<string, string> } = {
      headers: {
        'User-Agent': userAgent,
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        Connection: 'keep-alive',
      }
    };

    if (timeout) {
      config.timeout = timeout;
    }

    return await this.httpClient.get(url, config);
  }

  /**
   * Detects if the response is a security block page
   */
  private static isSecurityBlock(content: string): boolean {
    if (!content || content.length < 2000) {
      return true; // Very short content is likely a block page
    }

    const blockIndicators = [
      /cloudflare/i,
      /incapsula/i,
      /access\s*denied/i,
      /blocked/i,
      /security\s*check/i,
      /bot\s*protection/i,
      /ddos\s*protection/i,
      /ray\s*id/i, // Cloudflare Ray ID
      /reference\s*#/i, // Incapsula reference
      /please\s*enable\s*javascript/i,
      /checking\s*your\s*browser/i,
    ];

    return blockIndicators.some(indicator => indicator.test(content));
  }

  /**
   * Find privacy policy URLs from a website
   * @param targetUrl - The main website URL to search
   * @returns Promise<string[]> - Array of privacy policy URLs found
   */
  static async findPrivacyPolicyUrls(targetUrl: string): Promise<string[]> {
    try {
      const htmlContent = await this.fetchPageContent(targetUrl);
      const privacyLinks = this.findPrivacyPolicyLinks(htmlContent, targetUrl);
      return privacyLinks.map((link) => link.url);
    } catch (error) {
      console.warn(`Failed to find privacy policy URLs for ${targetUrl}:`, error);
      return [];
    }
  }
}
